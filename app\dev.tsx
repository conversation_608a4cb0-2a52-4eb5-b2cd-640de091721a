import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';

export default function DevScreen() {
  const sendMockNotification = async () => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '分红提醒测试',
          body: '测试股票 (TEST001) 今日有分红，每100股派 88.88元',
          data: {
            type: 'dividend_test',
            stockCode: 'TEST001',
            stockName: '测试股票',
            amount: '88.88元'
          },
        },
        trigger: null,
      });

      Alert.alert('测试通知已发送');
    } catch (error) {
      console.error('Failed to send test notification:', error);
      Alert.alert('发送失败');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Dev Tools</Text>

      <TouchableOpacity style={styles.button} onPress={sendMockNotification}>
        <Text style={styles.buttonText}>发送测试通知</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
});
