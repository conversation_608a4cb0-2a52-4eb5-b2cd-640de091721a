import { useFocusEffect } from "expo-router";
import { useCallback, useState } from "react";
import { ActivityIndicator, FlatList, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import StockCard from "../components/StockCard";
import { CalendarInfo } from "./api";
import { useCalendarStore, useFollowingStore } from "./store";

const DailyDividends = ({ item }: { item: CalendarInfo }) => {
  const [expanded, setExpanded] = useState(false);
  const { following } = useFollowingStore();

  if (!item.list || item.list.length === 0) {
    return (
      <View style={styles.dayContainer}>
        <View style={styles.dateHeaderContainer}>
            <Text style={styles.dateHeader}>{item.dateStr}</Text>
            <Text style={styles.noDividendBadge}>无分红</Text>
        </View>
      </View>
    );
  }

  const followedStocks = item.list.filter(stock => following.includes(stock.code));
  const unfollowedStocks = item.list.filter(stock => !following.includes(stock.code));

  return (
    <View style={styles.dayContainer}>
      <View style={styles.dateHeaderContainer}>
        <Text style={styles.dateHeader}>{item.dateStr}</Text>
        {followedStocks.length === 0 && <Text style={styles.noDividendBadge}>无关注</Text>}
      </View>
      {followedStocks.length > 0 && followedStocks.map(stock => <StockCard key={stock.code} stock={stock} />)}

      {unfollowedStocks.length > 0 && (
        <TouchableOpacity onPress={() => setExpanded(!expanded)} style={styles.toggleButton}>
          <Text style={styles.toggleButtonText}>{expanded ? "收起" : "展开"} ({unfollowedStocks.length})</Text>
        </TouchableOpacity>
      )}

      {expanded && unfollowedStocks.map(stock => <StockCard key={stock.code} stock={stock} />)}
    </View>
  );
};

export default function Index() {
  const { calendarData, loading, fetchCalendarData } = useCalendarStore();

  const fetchData = useCallback(async () => {
    const today = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + 30); // Fetch for 30 days

    const startDate = today.toISOString().split('T')[0];
    const endDate = thirtyDaysFromNow.toISOString().split('T')[0];

    await fetchCalendarData(startDate, endDate);
  }, [fetchCalendarData]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData])
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <FlatList
      data={calendarData}
      keyExtractor={(item) => item.date}
      renderItem={({ item }) => <DailyDividends item={item} />}
      contentContainerStyle={styles.listContainer}
    />
  );
}

const styles = StyleSheet.create({
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: '#f0f0f0',
  },
  listContainer: {
    paddingVertical: 2,
    backgroundColor: '#f5f5f5',
  },
  dayContainer: {
    marginBottom: 6,
  },
  dateHeaderContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      backgroundColor: '#fff',
      borderBottomWidth: 1,
      borderBottomColor: '#e0e0e0',
  },
  dateHeader: {
    fontSize: 17,
    fontWeight: "bold",
    color: '#2c3e50',
  },
  badgeContainer: {
    paddingHorizontal: 10,
    paddingBottom: 5,
  },
  noDividendBadge: {
      backgroundColor: '#f8f9fa',
      color: '#6c757d',
      fontSize: 11,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 3,
      overflow: 'hidden',
      alignSelf: 'flex-start',
  },
  toggleButton: {
    backgroundColor: '#fff',
    padding: 6,
    marginHorizontal: 8,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 2,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  toggleButtonText: {
    color: '#495057',
    fontWeight: '500',
    fontSize: 11,
  },
});
