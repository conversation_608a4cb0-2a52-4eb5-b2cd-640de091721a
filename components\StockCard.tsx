import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Stock } from '../app/api';

interface StockCardProps {
  stock: Stock;
}

// Function to calculate dividend per 100 shares
const calculateDividend = (diviCash: string): string => {
  const cash = parseFloat(diviCash.replace('元', ''));
  if (isNaN(cash)) {
    return diviCash; // Return original if parsing fails
  }
  // Assuming the API provides dividend per 10 shares, common in CN market
  return `${(cash * 10).toFixed(2)}元`;
};

const StockCard: React.FC<StockCardProps> = ({ stock }) => {
  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{stock.name}</Text>
        <Text style={styles.code}>{stock.code}</Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.dividendLabel}>每100股派</Text>
        <Text style={styles.dividendValue}>{calculateDividend(stock.diviCash)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 0,
    padding: 16,
    marginVertical: 0,
    marginHorizontal: 0,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f3f4',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: '#212529',
  },
  code: {
    fontSize: 11,
    color: '#6c757d',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: '500',
    marginLeft: 8,
  },
  cardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dividendLabel: {
    fontSize: 13,
    color: '#6c757d',
    fontWeight: '500',
  },
  dividendValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#dc3545',
  },
});

export default StockCard;
