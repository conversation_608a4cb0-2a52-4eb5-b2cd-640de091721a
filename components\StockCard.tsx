import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Stock } from '../app/api';

interface StockCardProps {
  stock: Stock;
}

// Function to calculate dividend per 100 shares
const calculateDividend = (diviCash: string): string => {
  const cash = parseFloat(diviCash.replace('元', ''));
  if (isNaN(cash)) {
    return diviCash; // Return original if parsing fails
  }
  // Assuming the API provides dividend per 10 shares, common in CN market
  return `${(cash * 10).toFixed(2)}元`;
};

const StockCard: React.FC<StockCardProps> = ({ stock }) => {
  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{stock.name}</Text>
        <Text style={styles.code}>{stock.code}</Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.dividendLabel}>每100股派</Text>
        <Text style={styles.dividendValue}>{calculateDividend(stock.diviCash)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 6,
    padding: 12,
    marginVertical: 2,
    marginHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    borderLeftWidth: 3,
    borderLeftColor: '#e74c3c',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  name: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  code: {
    fontSize: 11,
    color: '#7f8c8d',
    marginLeft: 6,
    backgroundColor: '#ecf0f1',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 2,
  },
  cardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: 8,
  },
  dividendLabel: {
    fontSize: 13,
    color: '#7f8c8d',
  },
  dividendValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
});

export default StockCard;
