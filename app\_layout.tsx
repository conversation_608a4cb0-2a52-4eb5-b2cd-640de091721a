import Constants from 'expo-constants';
import * as Notifications from 'expo-notifications';
import { Tabs } from "expo-router";
import { useEffect } from "react";
import { checkDividendsAndNotify, registerBackgroundFetchAsync } from "./notifications";

export default function AppLayout() {
  const isDev = __DEV__ || Constants.expoConfig?.extra?.isDev;

  useEffect(() => {
    const setupNotifications = async () => {
      await registerBackgroundFetchAsync();
      // Check for dividends on app start
      await checkDividendsAndNotify();
    };

    setupNotifications();

    // Listen for notification responses
    const subscription = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data;
      if (data?.type === 'dividend_summary') {
        console.log('User tapped dividend notification:', data.stocks);
        // Could navigate to calendar tab or show specific stocks
      } else if (data?.type === 'dividend_test') {
        console.log('User tapped test notification:', data);
        // Test notification was tapped
      }
    });

    return () => subscription.remove();
  }, []);

  return (
    <Tabs
      screenOptions={{
        tabBarIconStyle: { display: "none" },
        tabBarLabelStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
        tabBarStyle: {
            height: 80,
            paddingBottom: 16,
        }
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "日历",
        }}
      />
      <Tabs.Screen
        name="following"
        options={{
          title: "关注",
        }}
      />
      {isDev && (
        <Tabs.Screen
          name="dev"
          options={{
            title: "Dev",
          }}
        />
      )}
    </Tabs>
  );
}
