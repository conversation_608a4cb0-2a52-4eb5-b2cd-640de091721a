import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Stock } from '../app/api';

interface StockCardProps {
  stock: Stock;
}

// Function to calculate dividend per 100 shares
const calculateDividend = (diviCash: string): string => {
  const cash = parseFloat(diviCash.replace('元', ''));
  if (isNaN(cash)) {
    return diviCash; // Return original if parsing fails
  }
  // Assuming the API provides dividend per 10 shares, common in CN market
  return `${(cash * 10).toFixed(2)}元`;
};

const StockCard: React.FC<StockCardProps> = ({ stock }) => {
  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.name}>{stock.name}</Text>
        <Text style={styles.code}>{stock.code}</Text>
      </View>
      <View style={styles.cardBody}>
        <Text style={styles.dividendLabel}>每100股派</Text>
        <Text style={styles.dividendValue}>{calculateDividend(stock.diviCash)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginVertical: 0,
    marginHorizontal: 12,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#FF6B35',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  name: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1a1a1a',
  },
  code: {
    fontSize: 11,
    color: '#666',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 12,
    fontWeight: '600',
    marginLeft: 10,
  },
  cardBody: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fafafa',
    padding: 12,
    borderRadius: 6,
    marginTop: 4,
  },
  dividendLabel: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  dividendValue: {
    fontSize: 20,
    fontWeight: '800',
    color: '#FF6B35',
  },
});

export default StockCard;
