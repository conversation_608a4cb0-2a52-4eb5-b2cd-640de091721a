import AsyncStorage from '@react-native-async-storage/async-storage';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { CalendarInfo, getFinanceCalendar } from './api';

interface FollowingState {
  following: string[];
  toggleFollow: (code: string) => void;
}

interface CalendarState {
  calendarData: CalendarInfo[];
  loading: boolean;
  lastFetch: number;
  fetchCalendarData: (startDate: string, endDate: string) => Promise<void>;
  clearCalendarData: () => void;
}

export const useFollowingStore = create<FollowingState>()(
  persist(
    (set) => ({
      following: [],
      toggleFollow: (code) =>
        set((state) => ({
          following: state.following.includes(code)
            ? state.following.filter((c) => c !== code)
            : [...state.following, code],
        })),
    }),
    {
      name: 'following-storage', // unique name
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes cache

export const useCalendarStore = create<CalendarState>()((set, get) => ({
  calendarData: [],
  loading: false,
  lastFetch: 0,
  fetchCalendarData: async (startDate: string, endDate: string) => {
    const state = get();
    const now = Date.now();

    // Check if we have recent data
    if (state.calendarData.length > 0 && now - state.lastFetch < CACHE_DURATION) {
      return;
    }

    set({ loading: true });
    try {
      const data = await getFinanceCalendar(startDate, endDate);
      set({
        calendarData: data,
        loading: false,
        lastFetch: now
      });
    } catch (error) {
      console.error('Failed to fetch calendar data:', error);
      set({ loading: false });
    }
  },
  clearCalendarData: () => set({ calendarData: [], lastFetch: 0 }),
}));
