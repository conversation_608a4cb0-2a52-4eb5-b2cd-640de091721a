import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import * as Notifications from 'expo-notifications';

export default function DevScreen() {
  const sendMockNotification = async () => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎉 分红提醒测试',
          body: '测试股票 (TEST001) 今日有分红，每100股派 88.88元',
          data: { 
            type: 'dividend_test',
            stockCode: 'TEST001', 
            stockName: '测试股票',
            amount: '88.88元'
          },
        },
        trigger: null, // Immediate notification
      });
      
      Alert.alert('成功', '测试通知已发送！');
    } catch (error) {
      console.error('Failed to send test notification:', error);
      Alert.alert('错误', '发送测试通知失败');
    }
  };

  const clearAllNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await Notifications.dismissAllNotificationsAsync();
      Alert.alert('成功', '已清除所有通知！');
    } catch (error) {
      console.error('Failed to clear notifications:', error);
      Alert.alert('错误', '清除通知失败');
    }
  };

  const checkPermissions = async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      Alert.alert('通知权限状态', `当前状态: ${status}`);
    } catch (error) {
      console.error('Failed to check permissions:', error);
      Alert.alert('错误', '检查权限失败');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>开发者工具</Text>
      <Text style={styles.subtitle}>通知测试功能</Text>
      
      <TouchableOpacity style={styles.button} onPress={sendMockNotification}>
        <Text style={styles.buttonText}>📱 发送测试通知</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={clearAllNotifications}>
        <Text style={styles.buttonText}>🗑️ 清除所有通知</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.button} onPress={checkPermissions}>
        <Text style={styles.buttonText}>🔍 检查通知权限</Text>
      </TouchableOpacity>

      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>测试说明:</Text>
        <Text style={styles.infoText}>• 点击"发送测试通知"会立即发送一条模拟分红通知</Text>
        <Text style={styles.infoText}>• 通知内容包含测试股票的分红信息</Text>
        <Text style={styles.infoText}>• 可以用来测试通知权限和显示效果</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    marginTop: 30,
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
    lineHeight: 20,
  },
});
