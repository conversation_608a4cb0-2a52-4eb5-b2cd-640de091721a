import * as Notifications from 'expo-notifications';
import * as TaskManager from 'expo-task-manager';
import { getFinanceCalendar } from './api';
import { useFollowingStore } from './store';

const BACKGROUND_NOTIFICATION_TASK = 'BACKGROUND-NOTIFICATION-TASK';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

TaskManager.defineTask(BACKGROUND_NOTIFICATION_TASK, async () => {
  try {
    const { following } = useFollowingStore.getState();
    if (following.length === 0) {
      return;
    }

    const todayStr = new Date().toISOString().split('T')[0];
    const calendar = await getFinanceCalendar(todayStr, todayStr);
    const todayDividends = calendar.find(c => c.date === todayStr);

    if (todayDividends && todayDividends.list) {
      for (const stock of todayDividends.list) {
        if (following.includes(stock.code)) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: `分红提醒: ${stock.name} (${stock.code})`,
              body: `今日有分红，每100股派 ${stock.diviCash}`,
            },
            trigger: {
              type: Notifications.SchedulableTriggerInputTypes.DAILY,
              hour: 20,
              minute: 0
            },
          });
        }
      }
    }
  } catch (error) {
    console.error('Background notification task failed:', error);
  }
});

export const registerBackgroundFetchAsync = async () => {
  // Request notification permissions
  const { status } = await Notifications.requestPermissionsAsync();
  if (status !== 'granted') {
    console.warn('Notification permissions not granted');
    return;
  }

  await Notifications.cancelAllScheduledNotificationsAsync(); // Clear old notifications

  // Note: Background tasks are typically registered by other libraries like BackgroundFetch
  // For notifications, we rely on the scheduled notifications system instead
  console.log('Background notification task setup completed');
};
